"use strict";
cc._RF.push(module, 'd06f40RGNtELrdV8XT/vrpM', 'PlayerGameController ');
// scripts/pfb/PlayerGameController .ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerGameController = /** @class */ (function (_super) {
    __extends(PlayerGameController, _super);
    function PlayerGameController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.flagNode = null; //旗子节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        return _this;
        // update (dt) {}
    }
    PlayerGameController.prototype.start = function () {
    };
    PlayerGameController.prototype.setData = function (user) {
        var _this = this;
        this.scheduleOnce(function () {
            if (user == null) {
                _this.avatar.active = false;
            }
            else {
                Tools_1.Tools.setNodeSpriteFrameUrl(_this.avatar, user.avatar); //添加头像
                _this.avatar.active = true;
            }
        }, 0.1);
    };
    /**
     * 显示加分效果，带动画
     * @param addValue 加分数值
     */
    PlayerGameController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            else {
                console.warn("PlayerGame addScoreNode中找不到change_score子节点");
            }
            // 设置最高层级，确保不被头像遮挡
            this.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 5;
            // 停止之前的动画
            cc.Tween.stopAllByTarget(this.addScoreNode);
            // 重置节点状态
            this.addScoreNode.active = true;
            this.addScoreNode.opacity = 0;
            this.addScoreNode.scale = 0.8;
            // 保存原始位置
            var originalY_1 = this.addScoreNode.y;
            // 使用新的Tween API
            cc.tween(this.addScoreNode)
                .parallel(cc.tween().to(0.15, { opacity: 255 }), cc.tween().to(0.15, { scale: 1.1 }), cc.tween().by(0.15, { y: 15 }))
                .to(0.1, { scale: 1.0 })
                .delay(0.8)
                .parallel(cc.tween().to(0.25, { opacity: 0 }), cc.tween().to(0.25, { scale: 0.9 }), cc.tween().by(0.25, { y: 8 }))
                .call(function () {
                _this.addScoreNode.active = false;
                _this.addScoreNode.opacity = 255;
                _this.addScoreNode.scale = 1.0;
                _this.addScoreNode.y = originalY_1;
            })
                .start();
        }
        else {
            console.warn("PlayerGame addScoreNode未设置");
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerGameController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            else {
                console.warn("PlayerGame subScoreNode中找不到change_score子节点");
            }
            this.subScoreNode.active = true;
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
        else {
            console.warn("PlayerGame subScoreNode未设置");
        }
    };
    // 隐藏加减分节点
    PlayerGameController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "flagNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "subScoreNode", void 0);
    PlayerGameController = __decorate([
        ccclass
    ], PlayerGameController);
    return PlayerGameController;
}(cc.Component));
exports.default = PlayerGameController;

cc._RF.pop();